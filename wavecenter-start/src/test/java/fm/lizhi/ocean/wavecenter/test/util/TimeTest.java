package fm.lizhi.ocean.wavecenter.test.util;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import fm.lizhi.ocean.wavecenter.provider.activitycenter.manager.ActivityTemplateScheduleRuleManager;

public class TimeTest {


    /**
     * 测试方法
     */
    public static void main(String[] args) {
        ActivityTemplateScheduleRuleManager manager = new ActivityTemplateScheduleRuleManager();

        System.out.println("=== 测试 checkTimeInRange 方法 ===\n");

        // 测试场景1：从午夜开始的时间区间 00:00:00-08:00:00
        System.out.println("场景1：规则时间区间 00:00:00-08:00:00（当天午夜到早上8点）");
        testTimeRange(manager, "00:00:00", "08:00:00", "01:00:00", "07:00:00", "应该通过");
        testTimeRange(manager, "00:00:00", "08:00:00", "00:00:00", "05:00:00", "应该通过");
        testTime<PERSON>ange(manager, "00:00:00", "08:00:00", "09:00:00", "10:00:00", "应该不通过");
        testTimeRange(manager, "00:00:00", "08:00:00", "23:00:00", "01:00:00", "跨天活动，应该不通过");
        System.out.println();

        // 测试场景2：跨天时间区间 08:00:00-00:00:00
        System.out.println("场景2：规则时间区间 08:00:00-00:00:00（早上8点到次日午夜）");
        testTimeRange(manager, "08:00:00", "00:00:00", "09:00:00", "23:00:00", "应该通过");
        testTimeRange(manager, "08:00:00", "00:00:00", "10:00:00", "00:00:00", "到次日00:00，应该通过");
        testTimeRange(manager, "08:00:00", "00:00:00", "22:00:00", "02:00:00", "跨天活动，应该通过");
        testTimeRange(manager, "08:00:00", "00:00:00", "07:00:00", "09:00:00", "开始时间早于规则，应该不通过");
        System.out.println();

        // 测试场景3：同一天内时间区间 08:00:00-18:00:00
        System.out.println("场景3：规则时间区间 08:00:00-18:00:00（当天早上8点到下午6点）");
        testTimeRange(manager, "08:00:00", "18:00:00", "09:00:00", "17:00:00", "应该通过");
        testTimeRange(manager, "08:00:00", "18:00:00", "10:00:00", "15:00:00", "应该通过");
        testTimeRange(manager, "08:00:00", "18:00:00", "07:00:00", "09:00:00", "开始时间早于规则，应该不通过");
        testTimeRange(manager, "08:00:00", "18:00:00", "16:00:00", "19:00:00", "结束时间晚于规则，应该不通过");
        System.out.println();

        // 测试场景4：特殊边界情况
        System.out.println("场景4：特殊边界情况");
        testTimeRange(manager, "00:00:00", "00:00:00", "12:00:00", "13:00:00", "规则开始结束都是00:00:00");
        testTimeRange(manager, "23:00:00", "01:00:00", "23:30:00", "00:30:00", "跨天规则23:00-01:00");
        System.out.println();
    }

    /**
     * 测试辅助方法
     */
    private static void testTimeRange(ActivityTemplateScheduleRuleManager manager,
                                      String ruleStart, String ruleEnd,
                                      String activityStart, String activityEnd,
                                      String description) {
        try {
            // 创建今天的DateTime对象
            DateTime ruleStartTime = DateUtil.parseTimeToday(ruleStart);
            DateTime ruleEndTime = DateUtil.parseTimeToday(ruleEnd);
            DateTime activityStartTime = DateUtil.parseTimeToday(activityStart);
            DateTime activityEndTime = DateUtil.parseTimeToday(activityEnd);

            boolean result = manager.checkTimeInRange(activityStartTime, activityEndTime, ruleStartTime, ruleEndTime);

            System.out.printf("规则[%s-%s] 活动[%s-%s] => %s (%s)%n",
                    ruleStart, ruleEnd, activityStart, activityEnd,
                    result ? "✓通过" : "✗不通过", description);

        } catch (Exception e) {
            System.out.printf("规则[%s-%s] 活动[%s-%s] => 异常: %s (%s)%n",
                    ruleStart, ruleEnd, activityStart, activityEnd, e.getMessage(), description);
        }
    }
}
