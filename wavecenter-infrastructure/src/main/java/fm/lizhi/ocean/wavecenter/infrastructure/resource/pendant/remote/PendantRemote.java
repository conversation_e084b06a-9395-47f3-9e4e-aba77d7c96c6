package fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.remote;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.remote.IRemote;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantDto;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantShowTimeParamDTO;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantStatusParamDTO;

import java.util.List;

public interface PendantRemote extends IRemote {

    /**
     * 根据ID列表批量查询挂件
     *
     * @param ids   挂件ID列表
     * @param appId 应用ID
     * @return 挂件列表
     */
    List<PendantDto> getByIds(List<Long> ids, int appId);

    /**
     * 更新挂件展示时间,状态，线上可见性
     * @param param
     * @return
     */
    Result<Void> updatePendantShowTimeAndStatus(PendantShowTimeParamDTO param);

    /**
     * 更新挂件状态
     * @param param
     * @return
     */
    Result<Void> updatePendantStatus(PendantStatusParamDTO param);

    /**
     * 根据挂件ID列表查询挂件及其条件
     * @param pendantId 挂件ID
     * @return 挂件列表
     */
    Result<PendantDto> getPendantWithConditions(long pendantId);

    /**
     * 更新挂件展示时间失败
     */
    int UPDATE_PENDANT_SHOW_TIME_FAIL = 1;

    /**
     * 更新挂件状态失败
     */
    int UPDATE_PENDANT_STATUS_FAIL = 2;

    /**
     * 挂件不存在
     */
    int UPDATE_PENDANT_STATUS_NOT_FOUND = 3;

    /**
     * 根据挂件ID列表查询挂件及其条件失败
     */
    int GET_PENDANT_WITH_CONDITIONS_FAIL = 4;
}
