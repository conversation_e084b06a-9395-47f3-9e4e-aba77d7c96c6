package fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.remote.PendantRemote;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.constants.PendantAndConditionEnvEnum;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantDto;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantShowTimeAndStatusParamDTO;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantShowTimeParamDTO;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantStatusParamDTO;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.manager.PendantManager;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 挂件管理器实现类
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class PendantManagerImpl implements PendantManager {

    @Autowired
    private PendantRemote pendantRemote;

    @Override
    public List<PendantDto> getByIds(List<Long> ids, int appId) {
        log.info("PendantManagerImpl getByIds, ids: {}, appId: {}", ids, appId);
        return pendantRemote.getByIds(ids, appId);
    }

    @Override
    public boolean updatePendantStatus(PendantStatusParamDTO param) {
        try {
            return RpcResult.isSuccess(pendantRemote.updatePendantStatus(param));
        } catch (Exception e) {
            log.error("updatePendantStatus error. param: {}", param, e);
            return false;
        }
    }

    @Override
    public PendantDto getPendantWithConditions(long pendantId) {
        try {
            Result<PendantDto>  result = pendantRemote.getPendantWithConditions(pendantId);
            if (RpcResult.isFail(result)) {
                log.error("getPendantWithConditions fail. pendantId: {}, rCode: {}", pendantId, result.rCode());
                return null;
            }
            return result.target();
        } catch (Exception e) {
            log.error("getPendantWithConditions error. pendantId: {}", pendantId, e);
            return null;
        }
    }

    @Override
    public boolean updatePendantShowTimeAndStatus(PendantShowTimeAndStatusParamDTO param) {

        Result<Void> result = pendantRemote.updatePendantShowTimeAndStatus(new PendantShowTimeParamDTO()
                .setPendantId(param.getPendantId())
                .setStartTime(param.getStartTime())
                .setEndTime(param.getEndTime())
                .setEnv(PendantAndConditionEnvEnum.ONLINE)
                .setStatus(param.getStatus()));

        if (RpcResult.isFail(result)) {
            log.warn("updatePendantShowTime fail. rCode={},pendantId={},startTime={},endTime={}", result.rCode(), param.getPendantId(), param.getStartTime(), param.getEndTime());
            return false;
        }

        return true;
    }

}
