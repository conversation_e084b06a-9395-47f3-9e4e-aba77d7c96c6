package fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.convert;

import fm.lizhi.hy.content.protocol.PendantServiceProto.PendantConditionProbuf;
import fm.lizhi.hy.content.protocol.PendantServiceProto.AllStatusPendantProbuf;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.constants.PendantAndConditionEnvEnum;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.constants.PendantConditionScopEnum;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.constants.PendantConditionStatusEnum;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantConditionDTO;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.PendantDto;
import fm.lizhi.pp.content.assistant.datas.ConditionInfo;
import fm.lizhi.pp.content.assistant.datas.PendantInfo;
import fm.lizhi.pp.content.assistant.protocol.PendantServiceProto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface PendantConvert {

    PendantConvert I = Mappers.getMapper(PendantConvert.class);

    List<PendantDto> toPendantDtoListPp(List<PendantServiceProto.PendantProbuf> target);

    @Mapping(target = "conditions", ignore = true)
    PendantDto toPendantDtoPp(PendantServiceProto.PendantProbuf target);

    List<PendantDto> toPendantDtoListHy(List<fm.lizhi.hy.content.protocol.PendantServiceProto.PendantProbuf> pendantsList);

    @Mapping(target = "conditions", ignore = true)
    PendantDto toPendantDtoHy(fm.lizhi.hy.content.protocol.PendantServiceProto.PendantProbuf pendant);

    @Mapping(target = "conditions", ignore = true)
    PendantDto toPendantDto(fm.lizhi.xm.content.protocol.PendantServiceProto.PendantProbuf pendant);


    @Mapping(target = "conditions", source =  "conditionInfoList")
    @Mapping(target = "imageUrl", source = "image")
    PendantDto toPendantDtoPp(PendantInfo pendant);

    @Mapping(target = "pendantId", source = "configId")
    @Mapping(target = "njGroupId", source = "njUserGroupId")
    @Mapping(target = "status", source = "status", qualifiedByName = "convertPpConditionStatus")
    @Mapping(target = "scope", source = "scope", qualifiedByName = "convertPpConditionScope")
    @Mapping(target = "env", source = "env", qualifiedByName = "convertPpConditionEnv")
    PendantConditionDTO toPendantConditionDtoPp(ConditionInfo pendantCondition);

    @Mapping(target = "conditions", source =  "conditionInfoList")
    @Mapping(target = "imageUrl", source = "image")
    PendantDto toPendantDtoXm(fm.lizhi.xm.content.bean.pendant.PendantInfo pendant);

    @Mapping(target = "pendantId", source = "configId")
    @Mapping(target = "njGroupId", source = "njUserGroupId")
    @Mapping(target = "status", source = "status", qualifiedByName = "convertXmConditionStatus")
    @Mapping(target = "scope", source = "scope", qualifiedByName = "convertXmConditionScope")
    @Mapping(target = "env", source = "env", qualifiedByName = "convertXmConditionEnv")
    PendantConditionDTO toPendantConditionDtoXm(fm.lizhi.xm.content.bean.ConditionInfo pendantCondition);

    @Mapping(target = "conditions", source =  "conditionsList")
    PendantDto toPendantDtoHy(AllStatusPendantProbuf pendant);

    @Mapping(target = "pendantId", source = "configId")
    @Mapping(target = "njGroupId", source = "njUserGroupId")
    @Mapping(target = "status", source = "status", qualifiedByName = "convertHyConditionStatus")
    @Mapping(target = "scope", source = "scope", qualifiedByName = "convertHyConditionScope")
    @Mapping(target = "env", source = "env", qualifiedByName = "convertHyConditionEnv")
    PendantConditionDTO toPendantConditionDtoHy(PendantConditionProbuf pendantCondition);

    @Named("convertXmConditionStatus")
    default Integer convertXmConditionStatus(Integer xmStatus) {
        if (xmStatus == null) {
            return null;
        }
        if (xmStatus.equals(fm.lizhi.xm.content.constants.DataConstants.PendantStatus.ONLINE)) {
            return PendantConditionStatusEnum.ONLINE.getStatus();
        } else if (xmStatus.equals(fm.lizhi.xm.content.constants.DataConstants.PendantStatus.OFFLINE)) {
            return PendantConditionStatusEnum.OFFLINE.getStatus();
        }
        return xmStatus;
    }

    @Named("convertHyConditionStatus")
    default Integer convertHyConditionStatus(Integer hyStatus) {
        if (hyStatus == null) {
            return null;
        }
        if (hyStatus.equals(fm.lizhi.hy.content.common.constants.DataConstants.PendantStatus.ONLINE)) {
            return PendantConditionStatusEnum.ONLINE.getStatus();
        } else if (hyStatus.equals(fm.lizhi.hy.content.common.constants.DataConstants.PendantStatus.OFFLINE)) {
            return PendantConditionStatusEnum.OFFLINE.getStatus();
        }
        return hyStatus;
    }

    @Named("convertPpConditionStatus")
    default Integer convertPpConditionStatus(Integer ppStatus) {
        if (ppStatus == null) {
            return null;
        }
        if (ppStatus.equals(fm.lizhi.pp.content.assistant.constants.DataConstants.PendantStatus.ONLINE)) {
            return PendantConditionStatusEnum.ONLINE.getStatus();
        } else if (ppStatus.equals(fm.lizhi.pp.content.assistant.constants.DataConstants.PendantStatus.OFFLINE)) {
            return PendantConditionStatusEnum.OFFLINE.getStatus();
        }
        return ppStatus;
    }

    @Named("convertXmConditionScope")
    default Integer convertXmConditionScope(Integer xmScope) {
        if (xmScope == null) {
            return null;
        }
        if (xmScope.equals(fm.lizhi.xm.content.constants.DataConstants.ConditionScope.ALL_USERS)) {
            return PendantConditionScopEnum.ALL_USERS.getScope();
        } else if (xmScope.equals(fm.lizhi.xm.content.constants.DataConstants.ConditionScope.RANGE_USERS)) {
            return PendantConditionScopEnum.RANGE_USERS.getScope();
        }
        return xmScope;
    }

    @Named("convertHyConditionScope")
    default Integer convertHyConditionScope(Integer hyScope) {
        if (hyScope == null) {
            return null;
        }
        if (hyScope.equals(fm.lizhi.hy.content.common.constants.DataConstants.ConditionScope.RANGE_USERS)) {
            return PendantConditionScopEnum.RANGE_USERS.getScope();
        } else if (hyScope.equals(fm.lizhi.hy.content.common.constants.DataConstants.ConditionScope.ALL_USERS)) {
            return PendantConditionScopEnum.ALL_USERS.getScope();
        }
        return hyScope;
    }

    @Named("convertPpConditionScope")
    default Integer convertPpConditionScope(Integer ppScope) {
        if (ppScope == null) {
            return null;
        }
        if (ppScope.equals(fm.lizhi.pp.content.assistant.constants.DataConstants.ConditionScope.RANGE_USERS)) {
            return PendantConditionScopEnum.RANGE_USERS.getScope();
        } else if (ppScope.equals(fm.lizhi.pp.content.assistant.constants.DataConstants.ConditionScope.ALL_USERS)) {
            return PendantConditionScopEnum.ALL_USERS.getScope();
        }
        return ppScope;
    }

    @Named("convertXmConditionEnv")
    default Integer convertXmConditionEnv(Integer xmEnv) {
        if (xmEnv == null) {
            return null;
        }
        if (xmEnv.equals(fm.lizhi.xm.content.constants.DataConstants.Env.PRE)) {
            return PendantAndConditionEnvEnum.PRE.getEnv();
        } else if (xmEnv.equals(fm.lizhi.xm.content.constants.DataConstants.Env.ONLNIE)) {
            return PendantAndConditionEnvEnum.ONLINE.getEnv();
        }
        return xmEnv;
    }

    @Named("convertHyConditionEnv")
    default Integer convertHyConditionEnv(Integer hyEnv) {
        if (hyEnv == null) {
            return null;
        }
        if (hyEnv.equals(fm.lizhi.hy.content.common.constants.DataConstants.Env.PRE)) {
            return PendantAndConditionEnvEnum.PRE.getEnv();
        } else if (hyEnv.equals(fm.lizhi.hy.content.common.constants.DataConstants.Env.ONLNIE)) {
            return PendantAndConditionEnvEnum.ONLINE.getEnv();
        }
        return hyEnv;
    }

    @Named("convertPpConditionEnv")
    default Integer convertPpConditionEnv(Integer ppEnv) {
        if (ppEnv == null) {
            return null;
        }
        if (ppEnv.equals(fm.lizhi.pp.content.assistant.constants.DataConstants.Env.PRE)) {
            return PendantAndConditionEnvEnum.PRE.getEnv();
        } else if (ppEnv.equals(fm.lizhi.pp.content.assistant.constants.DataConstants.Env.ONLNIE)) {
            return PendantAndConditionEnvEnum.ONLINE.getEnv();
        }
        return ppEnv;
    }

}
