package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityTemplateGiftTask;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityTemplateGiftTaskExample;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.mapper.ActivityTemplateGiftTaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@Slf4j
public class ActivityTemplateGiftTaskDao {

    @Autowired
    private ActivityTemplateGiftTaskMapper activityTemplateGiftTaskMapper;

    /**
     * 根据模板id查询礼物自动上下架任务
     *
     * @param appId
     * @param templateId
     * @return
     */
    public List<ActivityTemplateGiftTask> selectTaskByTemplateId(Integer appId, long templateId) {
        ActivityTemplateGiftTaskExample example = new ActivityTemplateGiftTaskExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andTemplateIdEqualTo(templateId);
        return activityTemplateGiftTaskMapper.selectByExample(example);
    }

}
