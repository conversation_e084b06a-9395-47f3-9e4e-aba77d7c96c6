package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityTemplateScheduleRule;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.mapper.ActivityTemplateScheduleRuleMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Repository
@Slf4j
public class ActivityTemplateScheduleRuleBgDao {

    @Autowired
    private ActivityTemplateScheduleRuleMapper activityTemplateScheduleRuleMapper;




    /**
     * 插入或更新，存在则更新
     */
    public boolean insertOrUpdate(Integer recurrenceType, Long recurrenceStartTime, Long recurrenceEndTime,
                                  Long activityStartTimeLimit, Long activityEndTimeLimit, Long templateId) {


        ActivityTemplateScheduleRule param = new ActivityTemplateScheduleRule();
        param.setTemplateId(templateId);
        param.setDeployEnv(ConfigUtils.getEnvRequired().name());

        ActivityTemplateScheduleRule templateScheduleRule = activityTemplateScheduleRuleMapper.selectOne(param);

        Date recurrenceStartTimeDate = recurrenceStartTime == null ? null : new Date(recurrenceStartTime);
        Date recurrenceEndTimeDate = recurrenceEndTime == null ? null : new Date(recurrenceEndTime);
        Date activityStartTimeLimitDate = activityStartTimeLimit == null ? null : new Date(activityStartTimeLimit);
        Date activityEndTimeLimitDate = activityEndTimeLimit == null ? null : new Date(activityEndTimeLimit);


        if (templateScheduleRule == null) {
            templateScheduleRule = new ActivityTemplateScheduleRule();
            templateScheduleRule.setTemplateId(templateId);
            templateScheduleRule.setDeployEnv(ConfigUtils.getEnvRequired().name());
            templateScheduleRule.setRecurrenceType(recurrenceType);
            templateScheduleRule.setStartTime(recurrenceStartTimeDate);
            templateScheduleRule.setEndTime(recurrenceEndTimeDate);
            templateScheduleRule.setStartDate(activityStartTimeLimitDate);
            templateScheduleRule.setEndDate(activityEndTimeLimitDate);
            templateScheduleRule.setCreateTime(new Date());
            templateScheduleRule.setModifyTime(new Date());
            return activityTemplateScheduleRuleMapper.insert(templateScheduleRule) > 0;
        }else {
            templateScheduleRule.setRecurrenceType(recurrenceType);
            templateScheduleRule.setStartTime(recurrenceStartTimeDate);
            templateScheduleRule.setEndTime(recurrenceEndTimeDate);
            templateScheduleRule.setStartDate(activityStartTimeLimitDate);
            templateScheduleRule.setEndDate(activityEndTimeLimitDate);
            templateScheduleRule.setModifyTime(new Date());
            return activityTemplateScheduleRuleMapper.updateByPrimaryKey(templateScheduleRule) > 0;
        }
    }

    /**
     * 获取日程规则
     */
    public ActivityTemplateScheduleRule getSchedule(long templateId) {
        ActivityTemplateScheduleRule param = new ActivityTemplateScheduleRule();
        param.setTemplateId(templateId);
        param.setDeployEnv(ConfigUtils.getEnvRequired().name());
        return activityTemplateScheduleRuleMapper.selectOne(param);
    }
}
