package fm.lizhi.ocean.wavecenter.provider.activitycenter.manager;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityTemplateScheduleRule;
import fm.lizhi.ocean.wavecenter.module.api.activitycenter.constants.ActivityTemplateRecurrenceEnum;
import fm.lizhi.ocean.wavecenter.provider.activitycenter.convert.ActivityTemplateScheduleRuleConvert;
import fm.lizhi.ocean.wavecenter.provider.activitycenter.datastore.ActivityTemplateScheduleRuleDao;
import fm.lizhi.ocean.wavecenter.provider.activitycenter.model.dto.ActivityTemplateScheduleRuleDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityTemplateScheduleRuleManager {

    @Autowired
    private ActivityTemplateScheduleRuleDao activityTemplateScheduleRuleDao;

    /**
     * 根据活动模板ID获取活动模板日程规则
     *
     * @param templateId
     * @return
     */
    public Optional<ActivityTemplateScheduleRuleDTO> getScheduleByTemplateId(Long templateId) {

        ActivityTemplateScheduleRule schedule = activityTemplateScheduleRuleDao.getScheduleByTemplateId(templateId);
        ActivityTemplateScheduleRuleDTO dto = ActivityTemplateScheduleRuleConvert.I.convertActivityTemplateScheduleRuleDTO(schedule);
        return Optional.ofNullable(dto);
    }

    /**
     * 校验活动模板日程规则
     *
     */
    public Boolean checkTemplateScheduleRule(Long templateId, Long startTime, Long endTime) {

        Optional<ActivityTemplateScheduleRuleDTO> optional = getScheduleByTemplateId(templateId);
        if (!optional.isPresent()){
            // 空配置，默认通过
            log.info("checkTemplateScheduleRule, templateId:{} not config, pass.", templateId);
            return true;
        }

        // 不在时间日期区间内
        ActivityTemplateScheduleRuleDTO rule = optional.get();
        if (rule.getStartDate() != null && rule.getEndDate() != null){
            boolean isInStartTime = DateUtil.isIn(new Date(startTime), rule.getStartDate(), rule.getEndDate());
            boolean isInEndTime = DateUtil.isIn(new Date(endTime), rule.getStartDate(), rule.getEndDate());
            log.info("checkTemplateScheduleRule, templateId:{}, startTime:{}, endTime:{}, isInStartTime:{}, isInEndTime:{}",
                    templateId, startTime, endTime, isInStartTime, isInEndTime);
            if (!isInStartTime || !isInEndTime){
                log.info("checkTemplateScheduleRule is not in time limit, templateId:{}, startTime:{}, endTime:{}, isInStartTime:{}, isInEndTime:{}",
                        templateId, startTime, endTime, isInStartTime, isInEndTime);
                return false;
            }
        }


        if (rule.getRecurrenceType().equals(ActivityTemplateRecurrenceEnum.DAILY.getType())){
            // 校验是否符合小时区间
            if (rule.getStartTime() != null && rule.getEndTime() != null){

                DateTime startDateTime = DateUtil.parseTimeToday(DateUtil.formatTime(new Date(startTime)));
                DateTime endDateTime = DateUtil.parseTimeToday(DateUtil.formatTime(new Date(endTime)));
                DateTime ruleStart = DateUtil.parseTimeToday(DateUtil.formatTime(rule.getStartTime()));
                DateTime ruleEnd = DateUtil.parseTimeToday(DateUtil.formatTime(rule.getEndTime()));

                // 处理跨天时间区间的情况（如08:00:00-00:00:00，表示08:00到次日00:00）
                boolean isInTimeRange = checkTimeInRange(startDateTime, endDateTime, ruleStart, ruleEnd);

                log.info("checkTemplateScheduleRule recurrenceType:Daily, templateId:{}, startTime:{}, endTime:{}, isInTimeRange:{}",
                        templateId, startTime, endTime, isInTimeRange);

                if (!isInTimeRange) {
                    log.info("checkTemplateScheduleRule recurrenceType:Daily is not in time limit, templateId:{}, startTime:{}, endTime:{}, isInTimeRange:{}",
                            templateId, startTime, endTime, isInTimeRange);
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 检查时间是否在指定的时间区间内，支持跨天时间区间
     *
     * @param startDateTime 活动开始时间
     * @param endDateTime 活动结束时间
     * @param ruleStart 规则开始时间
     * @param ruleEnd 规则结束时间
     * @return true表示在时间区间内
     */
    private boolean checkTimeInRange(DateTime startDateTime, DateTime endDateTime, DateTime ruleStart, DateTime ruleEnd) {
        // 获取时间部分进行比较（小时:分钟:秒）
        String ruleStartTime = DateUtil.format(ruleStart, "HH:mm:ss");
        String ruleEndTime = DateUtil.format(ruleEnd, "HH:mm:ss");
        String activityStartTime = DateUtil.format(startDateTime, "HH:mm:ss");
        String activityEndTime = DateUtil.format(endDateTime, "HH:mm:ss");

        log.info("checkTimeInRange - ruleStartTime:{}, ruleEndTime:{}, activityStartTime:{}, activityEndTime:{}",
                ruleStartTime, ruleEndTime, activityStartTime, activityEndTime);

        // 判断是否为跨天时间区间（结束时间为00:00:00表示次日00:00）
        boolean isCrossDayRange = "00:00:00".equals(ruleEndTime);

        if (isCrossDayRange) {
            // 跨天时间区间处理：如08:00:00-00:00:00（表示08:00到次日00:00）
            return checkCrossDayTimeRange(activityStartTime, activityEndTime, ruleStartTime);
        } else {
            // 同一天内的时间区间处理：如08:00:00-18:00:00
            return checkSameDayTimeRange(startDateTime, endDateTime, ruleStart, ruleEnd);
        }
    }

    /**
     * 检查跨天时间区间
     *
     * @param activityStartTime 活动开始时间（HH:mm:ss格式）
     * @param activityEndTime 活动结束时间（HH:mm:ss格式）
     * @param ruleStartTime 规则开始时间（HH:mm:ss格式）
     * @return true表示在时间区间内
     */
    private boolean checkCrossDayTimeRange(String activityStartTime, String activityEndTime, String ruleStartTime) {
        // 跨天区间：从ruleStartTime到次日00:00:00
        // 活动时间需要满足：
        // 1. 活动开始时间 >= 规则开始时间
        // 2. 活动结束时间 <= 次日00:00:00（即活动结束时间 <= 活动开始时间时表示跨天，或者活动结束时间为00:00:00）

        boolean startTimeValid = activityStartTime.compareTo(ruleStartTime) >= 0;

        // 检查活动是否也是跨天的，或者活动结束时间是否为00:00:00（表示到次日00:00）
        boolean endTimeValid = activityEndTime.equals("00:00:00") ||
                               activityEndTime.compareTo(activityStartTime) <= 0 ||
                               activityStartTime.compareTo(ruleStartTime) >= 0;

        log.info("checkCrossDayTimeRange - startTimeValid:{}, endTimeValid:{}", startTimeValid, endTimeValid);

        return startTimeValid && endTimeValid;
    }

    /**
     * 检查同一天内的时间区间
     *
     * @param startDateTime 活动开始时间
     * @param endDateTime 活动结束时间
     * @param ruleStart 规则开始时间
     * @param ruleEnd 规则结束时间
     * @return true表示在时间区间内
     */
    private boolean checkSameDayTimeRange(DateTime startDateTime, DateTime endDateTime, DateTime ruleStart, DateTime ruleEnd) {
        // 同一天内的时间区间，使用原有逻辑
        boolean isInStartTime = DateUtil.isIn(startDateTime, ruleStart, ruleEnd);
        boolean isInEndTime = DateUtil.isIn(endDateTime, ruleStart, ruleEnd);

        log.info("checkSameDayTimeRange - isInStartTime:{}, isInEndTime:{}", isInStartTime, isInEndTime);

        return isInStartTime && isInEndTime;
    }
}
