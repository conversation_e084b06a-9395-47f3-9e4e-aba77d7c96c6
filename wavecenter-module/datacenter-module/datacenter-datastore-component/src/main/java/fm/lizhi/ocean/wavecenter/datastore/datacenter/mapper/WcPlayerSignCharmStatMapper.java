package fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.constant.ParamContants;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseDeleteProvider;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseInsertProvider;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseSelectProvider;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseUpdateProvider;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcPlayerSignCharmStat;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcPlayerSignCharmStatExample;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcPlayerSignCharmStatMapper {

    /**
     * 根据实体对象的字段值查询多条记录，查询条件会跳过NULL值的字段。
     *
     * @param entity 实体对象
     * @return 实体对象列表
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "select")
    List<WcPlayerSignCharmStat> selectMany(WcPlayerSignCharmStat entity);

    /**
     * 根据实体对象的字段值查询单条记录，查询条件会跳过NULL值的字段。
     *
     * @param entity 实体对象
     * @return 单个实体对象
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "select")
    WcPlayerSignCharmStat selectOne(WcPlayerSignCharmStat entity);

    /**
     * 根据实体对象中主键（{@link javax.persistence.Id}标注）字段查询单条数据。
     *
     * @param entity 实体对象
     * @return 单个实体对象
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "selectByPrimaryKey")
    WcPlayerSignCharmStat selectByPrimaryKey(WcPlayerSignCharmStat entity);

    /**
     * 根据实体对象的字段值查询分页记录，查询条件会跳过NULL值的字段。
     *
     * @param entity 实体对象
     * @return 分页数据
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "selectPage")
    PageList<WcPlayerSignCharmStat> selectPage(@Param(ParamContants.ENTITIE) WcPlayerSignCharmStat entity, @Param(ParamContants.PAGE_NUMBER) int pageNumber, @Param(ParamContants.PAGE_SIZE) int pageSize);

    /**
     * 根据实体对象中主键字段（{@link javax.persistence.Id}标注）删除数据。
     *
     * @param entity 实体对象
     * @return 影响行数
     */
    @DeleteProvider(type = BaseDeleteProvider.class, method = "deleteByPrimaryKey")
    int deleteByPrimaryKey(WcPlayerSignCharmStat entity);

    /**
     * 将实体对象写入数据库，会跳过NULL值的字段。<br/>
     * 如果主键字段（{@link javax.persistence.Id}标注）有设置{@link javax.persistence.GeneratedValue}时，会自动生成主键并设置到实体对象中。
     *
     * @param entity 实体对象
     * @return 影响行数
     */
    @InsertProvider(type = BaseInsertProvider.class, method = "insert")
    int insert(WcPlayerSignCharmStat entity);

    /**
     * 批量将实体对象写入数据库（不会跳过NULL值)。<br/>
     * 如果字段有设置{@link javax.persistence.Id}和{@link javax.persistence.GeneratedValue}时，会自动生成值，并设置到实体类实例中。
     *
     * @param entities 实体对象列表
     * @return 影响行数
     */
    @InsertProvider(type = BaseInsertProvider.class, method = "batchInsert")
    int batchInsert(@Param(ParamContants.ENTITIES) List<WcPlayerSignCharmStat> entities);

    /**
     * 根据实体类中主键（{@link javax.persistence.Id}标注的字段）更新数据，会跳过NULL值的字段。
     *
     * @param entity 实体对象
     * @return 影响行数
     */
    @UpdateProvider(type = BaseUpdateProvider.class, method = "updateByPrimaryKey")
    int updateByPrimaryKey(WcPlayerSignCharmStat entity);

    /**
     * 根据example类生成WHERE条件查询总记录条数
     *
     * @param example
     * @return
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "countByExample")
    long countByExample(WcPlayerSignCharmStatExample example);

    /**
     * 根据example类生成WHERE条件删除记录
     *
     * @param example
     * @return
     */
    @DeleteProvider(type = BaseDeleteProvider.class, method = "deleteByExample")
    long deleteByExample(WcPlayerSignCharmStatExample example);

    /**
     * 根据example类生成WHERE条件查询记录数
     *
     * @param example
     * @return
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "selectByExample")
    List<WcPlayerSignCharmStat> selectByExample(WcPlayerSignCharmStatExample example);

    /**
     * 根据example类生成WHERE条件查询分页记录数
     *
     * @param example
     * @param pageNumber 页码
     * @param pageSize   每页数据大小
     * @return
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "pageByExample")
    PageList<WcPlayerSignCharmStat> pageByExample(@Param(ParamContants.EXAMPLE) WcPlayerSignCharmStatExample example, @Param(ParamContants.PAGE_NUMBER) int pageNumber, @Param(ParamContants.PAGE_SIZE) int pageSize);

    /**
     * 根据example类生成WHERE条件更新记录数，会跳过实体类对象中的NULL值的字段。<br/>
     *
     * @param entity  实体类对象
     * @param example
     * @return
     */
    @UpdateProvider(type = BaseUpdateProvider.class, method = "updateByExample")
    int updateByExample(@Param(ParamContants.ENTITIE) WcPlayerSignCharmStat entity, @Param(ParamContants.EXAMPLE) WcPlayerSignCharmStatExample example);

    /**
     * 初始化记录，存在则跳过
     *
     * @param entity 实体类对象
     * @return 影响行数
     */
    @Insert("INSERT IGNORE INTO wavecenter_player_sign_charm_stat (id, app_id, family_id, room_id, user_id, value, stat_day) " +
            "VALUES (#{id}, #{appId}, #{familyId}, #{roomId}, #{userId}, #{value}, #{statDay})")
    int insertIgnore(WcPlayerSignCharmStat entity);

    /**
     * 累加魅力值
     *
     * @param appId          应用ID
     * @param familyId       家族ID
     * @param roomId         房间ID
     * @param userId         用户ID
     * @param statDay        统计时间
     * @param incrementValue 魅力值
     * @return 影响行数
     */
    @Update("UPDATE wavecenter_player_sign_charm_stat " +
            "SET value = value + #{incrementValue} " +
            "WHERE app_id = #{appId} AND family_id = #{familyId} " +
            "AND room_id = #{roomId} AND user_id = #{userId} AND stat_day = #{statDay} AND #{incrementValue} != 0")
    int incrementValue(@Param("appId") Integer appId,
                       @Param("familyId") Long familyId,
                       @Param("roomId") Long roomId,
                       @Param("userId") Long userId,
                       @Param("statDay") Date statDay,
                       @Param("incrementValue") Integer incrementValue);
}
