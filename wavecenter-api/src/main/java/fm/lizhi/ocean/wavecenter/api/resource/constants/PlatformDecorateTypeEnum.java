package fm.lizhi.ocean.wavecenter.api.resource.constants;

import lombok.Getter;

import java.util.Arrays;

/**
 * 平台装扮类型枚举
 * <AUTHOR>
 * @date 2025/3/21 15:30
 */
@Getter
public enum PlatformDecorateTypeEnum {
    
    
    /**
     * 头像框
     */
    AVATAR(1, "头像框"),

    /**
     * 背景
     */
    BACKGROUND(2, "房间背景"),

    /**
     * 座驾
     */
    VEHICLE(3, "座驾"),

    /**
     * 勋章
     */
    MEDAL(4, "勋章"),
    
    /**
     * 用户官方认证
     */
    USER_GLORY(5, "官方认证"),


    /**
     * 气泡
     */
    BUBBLE(6, "气泡"),

    /**
     * 房间角标
     */
    ROOM_MARK(7, "房间角标")



    ;

    private int type;

    private String name;

    PlatformDecorateTypeEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

    /**
     * 根据类型获取枚举
     * @param type 类型
     * @return 枚举
     */
    public static PlatformDecorateTypeEnum getByType(int type) {
        return Arrays.stream(PlatformDecorateTypeEnum.values())
                .filter(decorateTypeEnum -> decorateTypeEnum.getType() == type)
                .findFirst()
                .orElse(null);
    }


}
